@echo off
title HVNC Build System - Simplified
color 0A

echo ===============================================
echo           HVNC Build System v4.0
echo         Simplified Single Configuration
echo ===============================================
echo.
echo Building HVNC with High Quality configuration...
echo.
echo Performance Settings:
echo  * JPEG Quality: 90 (High Quality)
echo  * Frame Rate: 30 FPS
echo  * Compression Level: 8
echo  * Optimized for design and detailed work
echo.
echo To modify these settings, edit common/CompileTimeConfig.h
echo.

REM Try to find MSBuild in common locations
set "MSBUILD_PATH="

REM VS2022 Community
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" set "MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"

REM VS2022 Professional
if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" set "MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"

REM VS2022 Enterprise
if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" set "MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"

REM VS2019 Community
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"

REM VS2019 Professional
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe"

REM VS2019 Enterprise
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe" set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe"

REM Check if MSBuild was found
if "%MSBUILD_PATH%"=="" (
    echo [ERROR] MSBuild not found. Please install Visual Studio 2019 or 2022.
    pause
    exit /b 1
)

echo [INFO] MSBuild found: %MSBUILD_PATH%
echo.

REM Clean previous builds
echo [STEP 1/4] Cleaning previous builds...
echo [INFO] Attempting to terminate any running server processes...
taskkill /f /im Server*.exe >nul 2>&1
echo [INFO] Attempting to terminate any running client processes...
taskkill /f /im Client*.exe >nul 2>&1
echo [INFO] Processes terminated.

REM Clean build directories
if exist "Client\_bin\Release\Win32" rmdir /s /q "Client\_bin\Release\Win32" >nul 2>&1
if exist "Server\_bin\Release\Win32" rmdir /s /q "Server\_bin\Release\Win32" >nul 2>&1
echo [SUCCESS] Previous builds cleaned
echo.

REM Build Client
echo [STEP 2/4] Building HVNC Client...
"%MSBUILD_PATH%" Client\HVNC.vcxproj /p:Configuration=Release /p:Platform=Win32 /p:TargetName="Client" /verbosity:minimal

if errorlevel 1 (
    echo [ERROR] Client build failed
    pause
    exit /b 1
)
echo [SUCCESS] Client built successfully
echo.

REM Build Server
echo [STEP 3/4] Building HVNC Server...
"%MSBUILD_PATH%" Server\Server.vcxproj /p:Configuration=Release /p:Platform=Win32 /p:TargetName="Server" /verbosity:minimal

if errorlevel 1 (
    echo [ERROR] Server build failed
    pause
    exit /b 1
)
echo [SUCCESS] Server built successfully
echo.

REM Verify builds
echo [STEP 4/4] Verifying builds...
if not exist "Client\_bin\Release\Win32\Client.exe" (
    echo [ERROR] Client executable not found
    pause
    exit /b 1
)

if not exist "Server\_bin\Release\Win32\Server.exe" (
    echo [ERROR] Server executable not found
    pause
    exit /b 1
)
echo [SUCCESS] All builds verified
echo.

echo ===============================================
echo              BUILD COMPLETED!
echo ===============================================
echo.
echo Client executable: Client\_bin\Release\Win32\Client.exe
echo Server executable: Server\_bin\Release\Win32\Server.exe
echo.
echo Compile-time features:
echo  - TurboJPEG compression (Quality: 90)
echo  - Differential capture (Threshold: 25)
echo  - Network protocol with 4-byte framing
echo  - Frame rate: 30 FPS
echo  - Compression level: 8
echo  - Profile: High Quality
echo.
echo Ready for deployment!
echo.
pause
