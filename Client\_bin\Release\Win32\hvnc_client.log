[2025-07-22 04:46:39.710] [INFO] HVNC Client logging initialized
[2025-07-22 04:46:39.717] [INFO] HVNC Client starting - Host: **************, Port: 4043
[2025-07-22 04:46:39.732] [INFO] Hidden desktop thread started successfully
[2025-07-22 04:46:39.738] [INFO] TurboJPEG wrapper initialized successfully
[2025-07-22 04:46:39.740] [INFO] TurboJPEG image processing initialized successfully
[2025-07-22 04:46:39.741] [INFO] Capture system initialized with TurboJPEG compression
[2025-07-22 04:46:39.742] [INFO] HVNC Profile: High Quality | JPEG Quality: 90 | Frame Rate: 30 FPS | Compression: 8
[2025-07-22 04:46:39.744] [INFO] DEBUG: Compile-time constants - Quality: 90, FPS: 30, Interval: 33
[2025-07-22 04:46:39.794] [INFO] Successfully connected to **************:4043
[2025-07-22 04:46:40.284] [INFO] Successfully connected to **************:4043
[2025-07-22 04:46:40.445] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:40.458] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 16ms
[2025-07-22 04:46:40.461] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:40.463] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:41.224] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:41.232] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 04:46:41.235] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:41.237] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:41.463] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:41.472] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 16ms
[2025-07-22 04:46:41.474] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:41.476] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:41.794] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:41.800] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 16ms
[2025-07-22 04:46:41.802] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:41.804] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:42.102] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:42.109] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 04:46:42.110] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:42.112] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:42.410] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:42.418] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 04:46:42.420] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:42.421] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:42.948] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:42.955] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 16ms
[2025-07-22 04:46:42.958] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:42.961] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:43.446] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:43.452] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 04:46:43.454] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:43.455] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:43.841] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:43.849] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 04:46:43.851] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:43.853] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:43.988] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:43.994] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 04:46:43.996] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:43.998] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:44.125] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:44.136] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 04:46:44.138] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:44.141] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:47.717] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:47.722] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 04:46:47.725] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:47.727] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:47.963] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:47.971] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 16ms
[2025-07-22 04:46:47.973] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:47.974] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:48.092] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:48.102] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 04:46:48.107] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:48.109] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:48.253] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:48.262] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 04:46:48.265] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:48.266] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:48.391] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:48.398] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 04:46:48.401] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:48.403] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:48.528] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:48.535] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 04:46:48.536] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:48.538] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:48.666] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:48.675] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 16ms
[2025-07-22 04:46:48.679] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:48.682] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:48.834] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:48.842] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 04:46:48.846] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:48.847] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:48.974] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:48.981] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 04:46:48.983] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:48.985] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:49.113] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:49.119] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 04:46:49.121] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:49.123] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:49.251] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:49.259] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 04:46:49.263] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:49.265] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:49.397] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:49.404] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 04:46:49.407] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:49.409] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:49.531] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:49.539] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 04:46:49.541] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:49.543] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:49.674] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:49.682] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 04:46:49.684] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:49.687] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:49.821] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:49.831] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 16ms
[2025-07-22 04:46:49.833] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:49.835] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:49.950] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:49.958] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 04:46:49.961] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:49.964] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:50.291] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:50.299] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 16ms
[2025-07-22 04:46:50.302] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:50.303] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:50.499] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:50.511] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 04:46:50.513] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:50.515] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:50.934] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:50.945] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 04:46:50.946] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:50.948] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 04:46:51.240] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-22 04:46:51.249] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 04:46:51.251] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 04:46:51.253] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
