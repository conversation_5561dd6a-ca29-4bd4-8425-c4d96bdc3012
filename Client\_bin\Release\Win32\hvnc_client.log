[2025-07-22 05:30:59.321] [INFO] HVNC Client logging initialized
[2025-07-22 05:30:59.328] [INFO] HVNC Client starting - Host: *************, Port: 4043
[2025-07-22 05:30:59.340] [INFO] Hidden desktop thread started successfully
[2025-07-22 05:30:59.341] [INFO] Windows version detected: 10.0 (Build 26100) - Windows 11: Yes
[2025-07-22 05:30:59.355] [INFO] TurboJPEG wrapper initialized successfully
[2025-07-22 05:30:59.358] [INFO] TurboJPEG image processing initialized successfully
[2025-07-22 05:30:59.361] [INFO] Capture system initialized with TurboJPEG compression
[2025-07-22 05:30:59.363] [INFO] HVNC Profile: High Quality | JPEG Quality: 65 | Frame Rate: 45 FPS | Compression: 6
[2025-07-22 05:30:59.366] [INFO] DEBUG: Compile-time constants - Quality: 65, FPS: 45, Interval: 22
[2025-07-22 05:30:59.367] [INFO] Windows 11 detected - adding initialization delay for stability
[2025-07-22 05:31:02.373] [INFO] Windows 11 detected - using compatibility mode for desktop creation
[2025-07-22 05:31:02.490] [INFO] Using current thread desktop for Windows 11 compatibility
[2025-07-22 05:31:02.672] [INFO] Successfully set thread desktop
[2025-07-22 05:31:02.682] [INFO] Windows 11: Set socket timeouts to prevent hanging
[2025-07-22 05:31:02.685] [INFO] Socket keep-alive enabled
[2025-07-22 05:31:02.718] [INFO] Successfully connected to *************:4043 (attempt 1)
[2025-07-22 05:31:02.721] [INFO] InputThread: Successfully set thread desktop
[2025-07-22 05:31:02.794] [INFO] Windows 11: Set socket timeouts to prevent hanging
[2025-07-22 05:31:02.799] [INFO] Socket keep-alive enabled
[2025-07-22 05:31:02.806] [INFO] Successfully connected to *************:4043 (attempt 1)
[2025-07-22 05:31:02.809] [INFO] DesktopThread: Successfully set thread desktop
[2025-07-22 05:31:03.174] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:03.185] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15571 bytes (1.2% ratio) in 0ms
[2025-07-22 05:31:03.187] [DEBUG] TurboJPEG compression successful: 784x561 -> 15571 bytes
[2025-07-22 05:31:03.190] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15571 bytes JPEG
[2025-07-22 05:31:03.626] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:03.636] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15571 bytes (1.2% ratio) in 0ms
[2025-07-22 05:31:03.638] [DEBUG] TurboJPEG compression successful: 784x561 -> 15571 bytes
[2025-07-22 05:31:03.642] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15571 bytes JPEG
[2025-07-22 05:31:04.055] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:04.064] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15503 bytes (1.2% ratio) in 15ms
[2025-07-22 05:31:04.067] [DEBUG] TurboJPEG compression successful: 784x561 -> 15503 bytes
[2025-07-22 05:31:04.071] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15503 bytes JPEG
[2025-07-22 05:31:04.541] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:04.551] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15571 bytes (1.2% ratio) in 0ms
[2025-07-22 05:31:04.554] [DEBUG] TurboJPEG compression successful: 784x561 -> 15571 bytes
[2025-07-22 05:31:04.556] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15571 bytes JPEG
[2025-07-22 05:31:04.989] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:04.997] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15503 bytes (1.2% ratio) in 0ms
[2025-07-22 05:31:05.000] [DEBUG] TurboJPEG compression successful: 784x561 -> 15503 bytes
[2025-07-22 05:31:05.002] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15503 bytes JPEG
[2025-07-22 05:31:05.408] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:05.417] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15571 bytes (1.2% ratio) in 0ms
[2025-07-22 05:31:05.420] [DEBUG] TurboJPEG compression successful: 784x561 -> 15571 bytes
[2025-07-22 05:31:05.422] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15571 bytes JPEG
[2025-07-22 05:31:06.108] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:06.118] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15503 bytes (1.2% ratio) in 0ms
[2025-07-22 05:31:06.120] [DEBUG] TurboJPEG compression successful: 784x561 -> 15503 bytes
[2025-07-22 05:31:06.122] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15503 bytes JPEG
[2025-07-22 05:31:06.486] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:06.496] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15571 bytes (1.2% ratio) in 0ms
[2025-07-22 05:31:06.499] [DEBUG] TurboJPEG compression successful: 784x561 -> 15571 bytes
[2025-07-22 05:31:06.502] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15571 bytes JPEG
[2025-07-22 05:31:06.858] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:06.868] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15571 bytes (1.2% ratio) in 0ms
[2025-07-22 05:31:06.871] [DEBUG] TurboJPEG compression successful: 784x561 -> 15571 bytes
[2025-07-22 05:31:06.873] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15571 bytes JPEG
[2025-07-22 05:31:07.647] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:07.655] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15459 bytes (1.2% ratio) in 0ms
[2025-07-22 05:31:07.657] [DEBUG] TurboJPEG compression successful: 784x561 -> 15459 bytes
[2025-07-22 05:31:07.660] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15459 bytes JPEG
[2025-07-22 05:31:08.076] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:08.085] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15474 bytes (1.2% ratio) in 0ms
[2025-07-22 05:31:08.087] [DEBUG] TurboJPEG compression successful: 784x561 -> 15474 bytes
[2025-07-22 05:31:08.089] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15474 bytes JPEG
[2025-07-22 05:31:08.609] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:08.618] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15358 bytes (1.2% ratio) in 0ms
[2025-07-22 05:31:08.620] [DEBUG] TurboJPEG compression successful: 784x561 -> 15358 bytes
[2025-07-22 05:31:08.622] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15358 bytes JPEG
[2025-07-22 05:31:09.107] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:09.119] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15412 bytes (1.2% ratio) in 0ms
[2025-07-22 05:31:09.120] [DEBUG] TurboJPEG compression successful: 784x561 -> 15412 bytes
[2025-07-22 05:31:09.122] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15412 bytes JPEG
[2025-07-22 05:31:09.453] [INFO] Starting Explorer on desktop: D450B2804BB42420325575
[2025-07-22 05:31:09.457] [INFO] Windows 11: Creating Explorer without desktop specification
[2025-07-22 05:31:09.474] [INFO] Explorer process created successfully
[2025-07-22 05:31:09.687] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:09.712] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15394 bytes (1.2% ratio) in 0ms
[2025-07-22 05:31:09.714] [DEBUG] TurboJPEG compression successful: 784x561 -> 15394 bytes
[2025-07-22 05:31:09.716] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15394 bytes JPEG
[2025-07-22 05:31:10.292] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:10.303] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15487 bytes (1.2% ratio) in 0ms
[2025-07-22 05:31:10.305] [DEBUG] TurboJPEG compression successful: 784x561 -> 15487 bytes
[2025-07-22 05:31:10.307] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15487 bytes JPEG
[2025-07-22 05:31:10.483] [INFO] Taskbar configured successfully
[2025-07-22 05:31:10.772] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:10.782] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15447 bytes (1.2% ratio) in 15ms
[2025-07-22 05:31:10.784] [DEBUG] TurboJPEG compression successful: 784x561 -> 15447 bytes
[2025-07-22 05:31:10.785] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15447 bytes JPEG
[2025-07-22 05:31:11.540] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:11.550] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15462 bytes (1.2% ratio) in 0ms
[2025-07-22 05:31:11.552] [DEBUG] TurboJPEG compression successful: 784x561 -> 15462 bytes
[2025-07-22 05:31:11.554] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15462 bytes JPEG
[2025-07-22 05:31:12.025] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:12.039] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15624 bytes (1.2% ratio) in 0ms
[2025-07-22 05:31:12.042] [DEBUG] TurboJPEG compression successful: 784x561 -> 15624 bytes
[2025-07-22 05:31:12.046] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15624 bytes JPEG
[2025-07-22 05:31:13.018] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:13.027] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15767 bytes (1.2% ratio) in 0ms
[2025-07-22 05:31:13.030] [DEBUG] TurboJPEG compression successful: 784x561 -> 15767 bytes
[2025-07-22 05:31:13.032] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15767 bytes JPEG
[2025-07-22 05:31:13.793] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:13.804] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15767 bytes (1.2% ratio) in 0ms
[2025-07-22 05:31:13.808] [DEBUG] TurboJPEG compression successful: 784x561 -> 15767 bytes
[2025-07-22 05:31:13.811] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15767 bytes JPEG
[2025-07-22 05:31:14.334] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:14.347] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15767 bytes (1.2% ratio) in 16ms
[2025-07-22 05:31:14.349] [DEBUG] TurboJPEG compression successful: 784x561 -> 15767 bytes
[2025-07-22 05:31:14.351] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15767 bytes JPEG
[2025-07-22 05:31:15.045] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:15.072] [DEBUG] TurboJPEG compression: 1520x825, 3762000 bytes -> 33243 bytes (0.9% ratio) in 15ms
[2025-07-22 05:31:15.075] [DEBUG] TurboJPEG compression successful: 1520x825 -> 33243 bytes
[2025-07-22 05:31:15.081] [DEBUG] BitmapToJpgOptimized: Compressed 1520x825 to 33243 bytes JPEG
[2025-07-22 05:31:15.748] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:15.771] [DEBUG] TurboJPEG compression: 1520x825, 3762000 bytes -> 33243 bytes (0.9% ratio) in 16ms
[2025-07-22 05:31:15.774] [DEBUG] TurboJPEG compression successful: 1520x825 -> 33243 bytes
[2025-07-22 05:31:15.777] [DEBUG] BitmapToJpgOptimized: Compressed 1520x825 to 33243 bytes JPEG
[2025-07-22 05:31:16.347] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:16.369] [DEBUG] TurboJPEG compression: 1520x825, 3762000 bytes -> 33243 bytes (0.9% ratio) in 15ms
[2025-07-22 05:31:16.373] [DEBUG] TurboJPEG compression successful: 1520x825 -> 33243 bytes
[2025-07-22 05:31:16.376] [DEBUG] BitmapToJpgOptimized: Compressed 1520x825 to 33243 bytes JPEG
[2025-07-22 05:31:17.099] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:17.125] [DEBUG] TurboJPEG compression: 1520x825, 3762000 bytes -> 33243 bytes (0.9% ratio) in 15ms
[2025-07-22 05:31:17.128] [DEBUG] TurboJPEG compression successful: 1520x825 -> 33243 bytes
[2025-07-22 05:31:17.131] [DEBUG] BitmapToJpgOptimized: Compressed 1520x825 to 33243 bytes JPEG
[2025-07-22 05:31:17.810] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:17.825] [DEBUG] TurboJPEG compression: 1520x825, 3762000 bytes -> 33243 bytes (0.9% ratio) in 0ms
[2025-07-22 05:31:17.828] [DEBUG] TurboJPEG compression successful: 1520x825 -> 33243 bytes
[2025-07-22 05:31:17.831] [DEBUG] BitmapToJpgOptimized: Compressed 1520x825 to 33243 bytes JPEG
[2025-07-22 05:31:18.342] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:18.363] [DEBUG] TurboJPEG compression: 1520x825, 3762000 bytes -> 33243 bytes (0.9% ratio) in 15ms
[2025-07-22 05:31:18.365] [DEBUG] TurboJPEG compression successful: 1520x825 -> 33243 bytes
[2025-07-22 05:31:18.369] [DEBUG] BitmapToJpgOptimized: Compressed 1520x825 to 33243 bytes JPEG
[2025-07-22 05:31:18.896] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:18.923] [DEBUG] TurboJPEG compression: 1520x825, 3762000 bytes -> 33243 bytes (0.9% ratio) in 16ms
[2025-07-22 05:31:18.925] [DEBUG] TurboJPEG compression successful: 1520x825 -> 33243 bytes
[2025-07-22 05:31:18.930] [DEBUG] BitmapToJpgOptimized: Compressed 1520x825 to 33243 bytes JPEG
[2025-07-22 05:31:19.627] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:19.640] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15767 bytes (1.2% ratio) in 0ms
[2025-07-22 05:31:19.642] [DEBUG] TurboJPEG compression successful: 784x561 -> 15767 bytes
[2025-07-22 05:31:19.644] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15767 bytes JPEG
[2025-07-22 05:31:20.246] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:31:20.257] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15767 bytes (1.2% ratio) in 0ms
[2025-07-22 05:31:20.260] [DEBUG] TurboJPEG compression successful: 784x561 -> 15767 bytes
[2025-07-22 05:31:20.263] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15767 bytes JPEG
[2025-07-22 05:31:20.496] [INFO] InputThread: Socket closed or failed to receive msg (WSA Error: 10060)
[2025-07-22 05:31:20.503] [INFO] InputThread: Socket closed
[2025-07-22 05:31:20.509] [INFO] TurboJPEG wrapper cleaned up
[2025-07-22 05:31:20.513] [INFO] TurboJPEG image processing cleaned up
[2025-07-22 05:31:20.517] [INFO] Hidden desktop thread finished
[2025-07-22 05:31:20.520] [INFO] HVNC Client logging shutdown
