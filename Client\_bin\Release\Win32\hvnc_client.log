[2025-07-22 05:07:49.214] [INFO] HVNC Client logging initialized
[2025-07-22 05:07:49.220] [INFO] HVNC Client starting - Host: **************, Port: 4043
[2025-07-22 05:07:49.238] [INFO] Hidden desktop thread started successfully
[2025-07-22 05:07:49.238] [INFO] Windows version detected: 10.0 (Build 26100) - Windows 11: Yes
[2025-07-22 05:07:49.251] [INFO] TurboJPEG wrapper initialized successfully
[2025-07-22 05:07:49.254] [INFO] TurboJPEG image processing initialized successfully
[2025-07-22 05:07:49.255] [INFO] Capture system initialized with TurboJPEG compression
[2025-07-22 05:07:49.258] [INFO] HVNC Profile: High Quality | JPEG Quality: 65 | Frame Rate: 45 FPS | Compression: 6
[2025-07-22 05:07:49.260] [INFO] DEBUG: Compile-time constants - Quality: 65, FPS: 45, Interval: 22
[2025-07-22 05:07:49.262] [INFO] Windows 11 detected - adding initialization delay for stability
[2025-07-22 05:07:52.272] [INFO] Windows 11 detected - using compatibility mode for desktop creation
[2025-07-22 05:07:52.278] [INFO] Using current thread desktop for Windows 11 compatibility
[2025-07-22 05:07:52.281] [INFO] Successfully set thread desktop
[2025-07-22 05:07:52.292] [INFO] Windows 11: Set socket timeouts to prevent hanging
[2025-07-22 05:07:52.933] [INFO] Successfully connected to **************:4043 (attempt 1)
[2025-07-22 05:07:52.937] [INFO] InputThread: Successfully set thread desktop
[2025-07-22 05:07:53.935] [INFO] Windows 11: Set socket timeouts to prevent hanging
[2025-07-22 05:07:54.336] [INFO] Successfully connected to **************:4043 (attempt 1)
[2025-07-22 05:07:54.342] [INFO] DesktopThread: Successfully set thread desktop
[2025-07-22 05:07:55.922] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:07:55.938] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 32528 bytes (2.5% ratio) in 15ms
[2025-07-22 05:07:55.941] [DEBUG] TurboJPEG compression successful: 784x561 -> 32528 bytes
[2025-07-22 05:07:55.944] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 32528 bytes JPEG
[2025-07-22 05:07:57.715] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:07:57.732] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 32546 bytes (2.5% ratio) in 0ms
[2025-07-22 05:07:57.734] [DEBUG] TurboJPEG compression successful: 784x561 -> 32546 bytes
[2025-07-22 05:07:57.735] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 32546 bytes JPEG
[2025-07-22 05:07:58.699] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:07:58.712] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 32460 bytes (2.5% ratio) in 0ms
[2025-07-22 05:07:58.715] [DEBUG] TurboJPEG compression successful: 784x561 -> 32460 bytes
[2025-07-22 05:07:58.717] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 32460 bytes JPEG
[2025-07-22 05:07:59.715] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:07:59.726] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 32535 bytes (2.5% ratio) in 0ms
[2025-07-22 05:07:59.729] [DEBUG] TurboJPEG compression successful: 784x561 -> 32535 bytes
[2025-07-22 05:07:59.731] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 32535 bytes JPEG
[2025-07-22 05:08:00.230] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:08:00.241] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 32646 bytes (2.5% ratio) in 0ms
[2025-07-22 05:08:00.243] [DEBUG] TurboJPEG compression successful: 784x561 -> 32646 bytes
[2025-07-22 05:08:00.246] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 32646 bytes JPEG
[2025-07-22 05:08:01.283] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:08:01.294] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 32479 bytes (2.5% ratio) in 0ms
[2025-07-22 05:08:01.296] [DEBUG] TurboJPEG compression successful: 784x561 -> 32479 bytes
[2025-07-22 05:08:01.298] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 32479 bytes JPEG
[2025-07-22 05:08:02.633] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:08:02.643] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 32485 bytes (2.5% ratio) in 16ms
[2025-07-22 05:08:02.645] [DEBUG] TurboJPEG compression successful: 784x561 -> 32485 bytes
[2025-07-22 05:08:02.647] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 32485 bytes JPEG
[2025-07-22 05:08:03.948] [INFO] InputThread: Socket closed
[2025-07-22 05:08:03.952] [INFO] TurboJPEG wrapper cleaned up
[2025-07-22 05:08:03.954] [INFO] TurboJPEG image processing cleaned up
[2025-07-22 05:08:03.957] [INFO] Hidden desktop thread finished
[2025-07-22 05:08:03.959] [INFO] HVNC Client logging shutdown
