[2025-07-22 05:44:38.003] [INFO] HVNC Client logging initialized
[2025-07-22 05:44:38.006] [INFO] HVNC Client starting - Host: *************, Port: 4043
[2025-07-22 05:44:38.017] [INFO] Hidden desktop thread started successfully
[2025-07-22 05:44:38.018] [INFO] Windows version detected: 10.0 (Build 26100) - Windows 11: Yes
[2025-07-22 05:44:38.024] [INFO] TurboJPEG wrapper initialized successfully
[2025-07-22 05:44:38.028] [INFO] TurboJPEG image processing initialized successfully
[2025-07-22 05:44:38.031] [INFO] Capture system initialized with TurboJPEG compression
[2025-07-22 05:44:38.033] [INFO] HVNC Profile: High Quality | JPEG Quality: 65 | Frame Rate: 45 FPS | Compression: 6
[2025-07-22 05:44:38.035] [INFO] DEBUG: Compile-time constants - Quality: 65, FPS: 45, Interval: 22
[2025-07-22 05:44:38.037] [INFO] Windows 11 detected - adding initialization delay for stability
[2025-07-22 05:44:41.052] [INFO] Windows 11 detected - attempting hidden desktop creation with enhanced compatibility
[2025-07-22 05:44:41.056] [INFO] Windows 11: Successfully created hidden desktop with enhanced flags: D450B2804BB42420325575
[2025-07-22 05:44:41.057] [INFO] Successfully set thread desktop
[2025-07-22 05:44:41.063] [INFO] Windows 11: Set socket timeouts to prevent hanging
[2025-07-22 05:44:41.065] [INFO] Socket keep-alive enabled
[2025-07-22 05:44:41.066] [INFO] Keep-alive configured: 300 seconds idle, 5 seconds interval
[2025-07-22 05:44:41.286] [INFO] Successfully connected to *************:4043 (attempt 1)
[2025-07-22 05:44:41.291] [INFO] InputThread: Successfully set thread desktop
[2025-07-22 05:44:41.425] [INFO] Windows 11: Set socket timeouts to prevent hanging
[2025-07-22 05:44:41.429] [INFO] Socket keep-alive enabled
[2025-07-22 05:44:41.430] [INFO] Keep-alive configured: 300 seconds idle, 5 seconds interval
[2025-07-22 05:44:41.705] [INFO] Successfully connected to *************:4043 (attempt 1)
[2025-07-22 05:44:41.710] [INFO] DesktopThread: Successfully set thread desktop
[2025-07-22 05:44:42.268] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:44:42.280] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 05:44:42.282] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 05:44:42.284] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 05:44:42.665] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:44:42.670] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 05:44:42.673] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 05:44:42.674] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 05:44:43.491] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:44:43.500] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 05:44:43.501] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 05:44:43.504] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 05:44:43.906] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:44:43.912] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 05:44:43.912] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 05:44:43.913] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 05:44:44.782] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:44:44.790] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 05:44:44.792] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 05:44:44.794] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 05:44:45.303] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:44:45.310] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 05:44:45.312] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 05:44:45.313] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 05:44:45.626] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:44:45.634] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 05:44:45.637] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 05:44:45.639] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 05:44:46.474] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:44:46.480] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 05:44:46.481] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 05:44:46.484] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 05:44:47.095] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:44:47.103] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 05:44:47.105] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 05:44:47.107] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 05:44:48.350] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:44:48.354] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 05:44:48.355] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 05:44:48.357] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 05:44:48.939] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:44:48.946] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 05:44:48.947] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 05:44:48.950] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 05:44:48.967] [INFO] Starting Explorer on desktop: D450B2804BB42420325575
[2025-07-22 05:44:48.969] [INFO] Windows 11: Creating Explorer on hidden desktop: D450B2804BB42420325575
[2025-07-22 05:44:48.974] [INFO] Explorer process created successfully
[2025-07-22 05:44:49.280] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:44:49.287] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 05:44:49.289] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 05:44:49.291] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 05:44:49.990] [INFO] Taskbar configured successfully
[2025-07-22 05:44:50.279] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:44:50.290] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 7683 bytes (0.6% ratio) in 0ms
[2025-07-22 05:44:50.292] [DEBUG] TurboJPEG compression successful: 784x561 -> 7683 bytes
[2025-07-22 05:44:50.294] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 7683 bytes JPEG
[2025-07-22 05:44:53.225] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:44:53.354] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15278 bytes (1.2% ratio) in 0ms
[2025-07-22 05:44:53.537] [DEBUG] TurboJPEG compression successful: 784x561 -> 15278 bytes
[2025-07-22 05:44:53.541] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15278 bytes JPEG
[2025-07-22 05:44:57.219] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:44:57.226] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15279 bytes (1.2% ratio) in 0ms
[2025-07-22 05:44:57.228] [DEBUG] TurboJPEG compression successful: 784x561 -> 15279 bytes
[2025-07-22 05:44:57.230] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15279 bytes JPEG
[2025-07-22 05:44:58.495] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:44:58.503] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15279 bytes (1.2% ratio) in 16ms
[2025-07-22 05:44:58.506] [DEBUG] TurboJPEG compression successful: 784x561 -> 15279 bytes
[2025-07-22 05:44:58.507] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15279 bytes JPEG
[2025-07-22 05:44:59.831] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 65 (compile-time: 65)
[2025-07-22 05:44:59.841] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 15279 bytes (1.2% ratio) in 0ms
[2025-07-22 05:44:59.843] [DEBUG] TurboJPEG compression successful: 784x561 -> 15279 bytes
[2025-07-22 05:44:59.845] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 15279 bytes JPEG
[2025-07-22 05:45:00.001] [INFO] InputThread: Socket closed or failed to receive msg (WSA Error: 10060)
[2025-07-22 05:45:00.006] [INFO] InputThread: Socket closed
[2025-07-22 05:45:00.010] [INFO] TurboJPEG wrapper cleaned up
[2025-07-22 05:45:00.012] [INFO] TurboJPEG image processing cleaned up
[2025-07-22 05:45:00.015] [INFO] Hidden desktop thread finished
[2025-07-22 05:45:00.017] [INFO] HVNC Client logging shutdown
